# 示例配置文件，展示如何使用本地映射功能
dynamic:
  enable: true
  local-mappings:
    # 正则表达式匹配，将任何服务名映射到 svc.k8s.test.hxyxt.com 域名
    '(.+)': '$1.svc.k8s.test.hxyxt.com'
    # 精确匹配，将 order-atom-service 映射到指定IP和端口
    'order-atom-service': '127.0.0.1:8080'
    # 精确匹配，将 user-service 映射到另一个Nacos服务名
    'user-service': 'user-service-yrk'
    # 支持多个目标实例，用逗号分隔（会随机选择）
    'payment-service': '*************:8080,*************:8080'

# 注意：
# 1. 精确匹配优先于正则表达式匹配
# 2. 正则表达式支持捕获组和替换，如 $1, $2 等
# 3. 目标可以是：IP:端口、服务名、或包含协议的完整URL
# 4. 只有当 allRoute 为空时才会使用本地映射配置
