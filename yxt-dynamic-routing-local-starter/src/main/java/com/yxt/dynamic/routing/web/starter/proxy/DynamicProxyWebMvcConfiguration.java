package com.yxt.dynamic.routing.web.starter.proxy;

import com.yxt.dynamic.routing.proxy.DynamicAround;
import com.yxt.dynamic.routing.proxy.DynamicBeanComponent;
import com.yxt.dynamic.routing.proxy.DynamicFeignRequestInterceptor;
import com.yxt.dynamic.routing.proxy.DynamicProperties;
import com.yxt.dynamic.routing.proxy.DynamicProxyInterceptor;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/9 14:35
 */
@ConditionalOnProperty(value = "dynamic.enable", havingValue = "true")
@Slf4j
@EnableConfigurationProperties(value = {DynamicProperties.class})
public class DynamicProxyWebMvcConfiguration implements WebMvcConfigurer {

    @Bean
    public WebMvcConfigurer dynamicProxyWebMvcConfigurer(){
        return new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                log.debug("添加动态路由拦截器 DynamicProxyInterceptor");
                registry.addInterceptor(new DynamicProxyInterceptor()).addPathPatterns("/**");
            }
        };
    }

    @Bean
    @ConditionalOnClass({feign.RequestInterceptor.class})
    public RequestInterceptor cloudContextInterceptor() {
        return new DynamicFeignRequestInterceptor();
    }

    @Bean
    public DynamicAround dynamicAround(){
        return new DynamicAround();
    }

    @Bean
    public DynamicBeanComponent dynamicBeanComponent(){
        return new DynamicBeanComponent();
    }

}
